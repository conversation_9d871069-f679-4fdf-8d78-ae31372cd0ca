@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

:root{
    --ts: 1.3rem;
    --motto: 9vmin;
    --motto-2: 2rem;
    --ts1: 2vw;
}

body{
    margin: 0;
    box-sizing: border-box;
    background-color: #caf0f8;
    overflow-x: hidden;
    background-image: linear-gradient(to left bottom,#caf0f8,#ade8f4);
}

/*---------HEADER STYLING--------*/
.header{
    background-color: #03045e;
    width: 99.7%;
    height: 12vmin;
    display: flex;
    flex-direction: row;
    font-weight: 500;
    /* -webkit-text-stroke: 1px black; */
    /* border: 2px solid black; */
    border-radius: 0px 0px 10px 10px;
}

.logo{
    display: block;
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: 10vmin;
    height: 10vmin;
    border: 2px solid rgb(137, 211, 137);
    border-radius: 50%;
    margin: 0.4% 0% 0% 2%;
    cursor: pointer;

}

.title{
    margin-top: 0.1%;
    font-size: var(--motto);
    text-shadow: 5px 5px 7px #000;
    margin-left: auto;
    margin-right: auto;
    color: white;
    /* font-family: 'Lobster', cursive; */
    transition: all .3s ease 0s;
    font-family:"Montserrat" ;

}
.title:hover{
    color: #90e0ef;
    transform: scale(1.1);
}
/*----------MAIN CONTENT STYILNG----------*/
.front{
    height: 38vw;
    width: 90vw ;
    margin: 0 auto;
    margin-top: 1%;
    /* display: grid;  */
    display: flex;
    font-family: "Montserrat"; 
    /* gap: 0px 30px; */
}

/* -- Custom Scrollbar Styling -- */
::-webkit-scrollbar{
    width: .9vw;
    background: #90e0ef;
    border-radius: 5px;
  }
  
  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #023e8a;
    border-radius: 10px;
    background-clip: padding-box;
  }
  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #03045e; 
  }

/*--------FRONT LEFT SIDE STYLING---------*/

.front-left{
    grid-area: front-left;
    height:max-content;
    box-sizing: border-box;
    width: 100%;
    text-align: center;
    font-size: var(--ts1);
}

.motto{
    padding: 3px;
    margin-left: 2%;
    margin-top: 10%;
    font-size: 3.1vw;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-weight: 600;
    font-family: "montserrat";
}

.formhead{
    font-family: "Monsterrat";

}
.start{
    background-color: #0077b6;
    height: 5vw;
    width: 10rem;
    border: none;
    border-radius: 30px;
    font-size: 1.5vw;
    font-family: "Montserrat";
    text-decoration:none;
    color: #e0fbfc;
    transition: all .3s ease 0s;
    box-shadow: 7px 7px 10px #000 ;

}
.start a{
    text-decoration:none;
    /* -webkit-text-stroke: 0.5px black; */
}

.start:hover{
    cursor:pointer;
    background-color: #48cae4;
    color: #03045e;
    box-shadow: 10px 10px 15px #000000;
    transform: scale(1.1);
    font-weight: 500;
}
/*--------FRONT RIGHT SIDE STYLING---------*/
.front-right{
    grid-area: front-right;
    height:80%;
    box-sizing: border-box;
    width: 90%;
}

.front img{
    padding-top: 3vw;
    height: 25vw;
    width: 85%;
}

/*------- FORM SCREEN STYLING----------*/
.entry-sec{
    height: 42vw;
    width: 90vw ;
    margin: 0 auto;
}

.motto-2{
    padding: 5px;
    margin-left: 2%;
    margin-top: 1%;
    color: #000000;
    font-size: var(--motto-2);
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-family: "Montserrat";
    font-weight: 300;
}
.motto-2-1{
    padding: 5px;
    margin-left: 2%;
    margin-top: 2%;
    font-size: 2rem;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-family: "Montserrat";
    font-weight: 600;
}
.form-body-2{
    display: flex;
    flex-direction: row;
    font-family: "Montserrat";
}

.form{
    background-color: #90e0ef;
    height: max-content;
    width: 40vw ;
    border-radius: 15px;
    margin: -2% auto;
    padding: 10px;
}

.formhead p{
    font: "Montserrat";
}

.form-body-2 img{
    height: 30vw;
    width: 30vw ;
    margin: -1% auto;
}

.form-body-2 ul{
    list-style: none;
    font-family: "Montserrat";
}
.form-sizing{
    margin-left: 8%;
    margin-top: 5%;
    font-size: 1.5vw;
    text-shadow: 0.3px 0.3px #000;
    font-family: "Montserrat";
}
.name{
    background-color: aliceblue;
    border-radius: 50px;
    padding: 7px;
    border: none;
    box-shadow: 2px 2px 10px #000;
    padding: 1vw;
    font: "Montserrat";
    
}
.submit-btn{
    background-color: #0077b6;
    height: 2rem;
    width: 7vw;
    border-radius: 30px;
    font-size: 1.3vw;
    text-decoration:none;
    margin-left: 30%;
    margin-bottom: 1%;
    font-family: "montserrat";
    transition: all .3s ease 0s;
    box-shadow: 4px 4px 6px #000;
    border: none;
    color: white;
    
}
.submit-btn:hover{
    cursor:pointer;
    background-color: #0076b69c;
    /* background-color: #89c23d; */
    box-shadow: 7px 7px 10px #000000;
    transform: scale(1.1);
}

/*--------INFORMATION SECTION STYLING---------*/
.info-1{
    margin-left: 3%;
    font-size: var(--ts);
    width: 90%;
    font-family: "montserrat";
}

.info-sec{
    width: 90vw ;
    margin:  auto;
}

.chakra{
    height: 30%;
    width: 30%;
    float:right;
    position: static;
    border: 5px solid var(--dark-green);
}

.headings{
    padding: 5px;
    font-size: 2rem;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-weight: 600;
    margin-left: 5%;
    font-family: "montserrat";
}

.vid{
    margin: 1% 5%;
    width: 90vw;
    height: 40vw;
    border: 5px solid black;
}
/*----------FOOTER STYLING----------*/
.footer-sec{
    width: 99.5%;
    background-color: #023e8a;
    margin-bottom: -1%;
    font-size: var(--ts);
    font-family: "montserrat";
}
.footer-1{
    display: flex;
    justify-content: space-evenly;
    color: white;
    text-shadow: 1px 1px 1px #000;
    font-family: "montserrat";
}
.footer-1 a{
    text-decoration: none;
    color:white;
    font-family: "montserrat";
}

/* .footer-2{
    margin: auto;
    width: 250px;
} */    

/*--------MEDIA QUERIES--------*/
@media only screen and (min-device-width: 1087px) and (max-device-width: 1250px) {
    :root{
        --motto-2: 2rem;
    }
  }
@media only screen and (min-device-width: 823px) and (max-device-width: 1086px) {
    .start{
        width: 7rem;
    }
    :root{
        --motto-2:2rem;
        --ts1: 2.5vw;
    }
}
@media only screen and (min-device-width: 759px) and (max-device-width: 822px) {
    :root{
        --motto-2: 2.2rem;
        --ts1: 2.5vw;
    }
    .start{
        width: 7rem;
    }
    .submit-btn{
        height: 1.5rem;
    }
    .motto-2-1{
        margin-top: 8%;
    }
}
@media only screen and (min-device-width: 675px) and (max-device-width: 758px) {
    :root{
        --ts1: 2.5vw;
        --motto-2: 2rem;
    }
    .motto-2-1{
        margin-top: 12%;
    }
    .start{
        width: 5rem;
    }
    .submit-btn{
        height: 1.5rem;
    }

}
@media only screen and (min-device-width: 631px) and (max-device-width: 675px) {
    :root{
        --ts1: 2.5vw;
        --motto-2: 2rem;
    }
    .motto-2-1{
        margin-top: 12%;
    }
    .start{
        width: 5rem;
        font-size: 1.5vw;
    }
    .submit-btn{
        height: 1.5rem;
    }

}
@media only screen and (min-device-width: 587px) and (max-device-width: 630px) {
    :root{
        --ts1: 2.5vw;
        --motto-2: 2rem;
    }
    .motto-2-1{
        margin-top: 15%;
    }
    .start{
        width: 5rem;
        font-size: 1.5vw;
    }
    .submit-btn{
        height: 1.5rem;
    }

}
@media only screen and (min-device-width: 503px) and (max-device-width: 586px) {
    :root{
        --ts1: 3vw;
        --motto-2: 2rem;
    }
    .motto-2{
        margin-top: 10%;
    }
    .motto-2-1{
        margin-top: 35%;
    }
    .start{
        width: 5rem;
        font-size: 1.5vw;
    }
    .submit-btn{
        height: 1.5rem;
    }
    .form-sizing{
        font-size: 2vw;
    }

}
@media only screen and (min-device-width: 451px) and (max-device-width: 502px) {
    :root{
        --ts1: 3vw;
        --motto-2: 1.5rem;
    }
    .motto-2{
        margin-top: 30%;
    }
    .motto-2-1{
        margin-top: 35%;
    }
    .start{
        width: 5rem;
        font-size: 1.5vw;
    }
    .submit-btn{
        height: 1.5rem;
    }
    .form-sizing{
        font-size: 2vw;
    }
    .form-body-2{
        display: flex;
        align-content: flex-start;
    }
    .name{
        width: 60%;
    }
    .front-left{
        font-size: var(--ts);
    }
}

@media only screen and (min-device-width: 400px) and (max-device-width: 451px) {
    :root{
        --ts1: 3vw;
        --motto-2: 1.5rem;
        --motto:2rem;
    }

    .motto-2{
        margin-top: 40%;
    }
    .motto-2-1{
        margin-top: 35%;
    }
    .start{
        width: 5rem;
        font-size: 1.5vw;
    }
    .submit-btn{
        height: 1.5rem;
    }
    .form-sizing{
        font-size: 2vw;
    }
    .form-body-2{
        display: flex;
        align-content: flex-start;
    }
    .name{
        width: 60%;
    }
    .front-left{
        font-size: var(--ts);
    }
    .info-1{
        font-size: .7rem;
    }
}

@media only screen and (min-device-width: 355px) and (max-device-width: 399px) {
    :root{
        --ts1: 0.8rem;
        --motto-2: 1rem;
        --motto:1.2rem;
    }

    .motto-2{
        margin-top: 80%;
    }
    .motto-2-1{
        margin-top: 35%;
    }
    .start{
        width: 5rem;
        font-size: 1.5vw;
    }
    .submit-btn{
        height: 1rem;
        width: 2rem;
    }
    .form-sizing{
        font-size: 2vw;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }
    .form-body-2{
        display: flex;
        align-content: flex-start;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }
    .name{
        width: 60%;
        padding: 0px;
        font-size: 9px;
    }
    .front-left{
        font-size: var(--ts);
    }
    .headings{
        font-size: 1rem;
    }
    .info-1{
        font-size: .7rem;
    }
}
