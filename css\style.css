@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

:root{
    --ts: 1.3rem;
    --motto: 9vmin;
    --motto-2: 2rem;
    --ts1: 2vw;
    --primary-blue: #03045e;
    --secondary-blue: #0077b6;
    --light-blue: #90e0ef;
    --accent-blue: #48cae4;
    --bg-gradient: linear-gradient(135deg, #caf0f8 0%, #ade8f4 50%, #90e0ef 100%);
}

* {
    box-sizing: border-box;
}

body{
    margin: 0;
    background: var(--bg-gradient);
    overflow-x: hidden;
    position: relative;
    animation: fadeIn 1s ease-in;
}

/* Particle background effect */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 177, 153, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    z-index: -1;
    animation: float 20s ease-in-out infinite;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(1deg); }
    66% { transform: translateY(-20px) rotate(-1deg); }
}

/*---------HEADER STYLING--------*/
.header{
    background: linear-gradient(135deg, var(--primary-blue) 0%, #0077b6 100%);
    width: 99.7%;
    height: 12vmin;
    display: flex;
    flex-direction: row;
    font-weight: 500;
    border-radius: 0px 0px 10px 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
    animation: slideDown 0.8s ease-out;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 3s infinite;
}

@keyframes slideDown {
    from { transform: translateY(-100%); }
    to { transform: translateY(0); }
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.logo{
    width: 10vmin;
    height: 10vmin;
    border: 3px solid var(--light-blue);
    border-radius: 50%;
    margin: 0.4% 0% 0% 2%;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    position: relative;
    overflow: hidden;
}

.logo::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
}

.logo:hover {
    transform: rotate(360deg) scale(1.1);
    border-color: var(--accent-blue);
    box-shadow: 0 0 20px rgba(72, 202, 228, 0.6);
}

.logo:hover::before {
    width: 100%;
    height: 100%;
}

.title{
    margin-top: 0.1%;
    font-size: var(--motto);
    text-shadow: 5px 5px 7px rgba(0, 0, 0, 0.7);
    margin-left: auto;
    margin-right: auto;
    color: white;
    transition: all .5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    font-family:"Montserrat";
    cursor: pointer;
    position: relative;
    background: linear-gradient(45deg, #ffffff, var(--light-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-blue), var(--light-blue));
    transform: translateX(-50%);
    transition: width 0.4s ease;
}

.title:hover{
    transform: scale(1.1) translateY(-2px);
    text-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
}

.title:hover::after {
    width: 100%;
}
/*----------MAIN CONTENT STYLING----------*/
.front{
    height: 38vw;
    width: 90vw;
    margin: 0 auto;
    margin-top: 1%;
    display: flex;
    font-family: "Montserrat";
    opacity: 0;
    animation: slideInUp 1s ease-out 0.3s forwards;
    position: relative;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* -- Custom Scrollbar Styling -- */
::-webkit-scrollbar{
    width: .9vw;
    background: #90e0ef;
    border-radius: 5px;
  }
  
  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #023e8a;
    border-radius: 10px;
    background-clip: padding-box;
  }
  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #03045e; 
  }

/*--------FRONT LEFT SIDE STYLING---------*/

.front-left{
    height: max-content;
    width: 100%;
    text-align: center;
    font-size: var(--ts1);
    animation: slideInLeft 1s ease-out 0.6s both;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.motto{
    padding: 3px;
    margin-left: 2%;
    margin-top: 10%;
    font-size: 3.1vw;
    font-family: "Montserrat";
    font-weight: 600;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    animation: textGlow 2s ease-in-out infinite alternate;
}

@keyframes textGlow {
    from {
        filter: drop-shadow(0 0 5px rgba(3, 4, 94, 0.3));
    }
    to {
        filter: drop-shadow(0 0 15px rgba(3, 4, 94, 0.6));
    }
}

.formhead{
    font-family: "Montserrat";
}

.start{
    background: linear-gradient(135deg, var(--secondary-blue), var(--accent-blue));
    height: 5vw;
    width: 10rem;
    border: none;
    border-radius: 30px;
    font-size: 1.5vw;
    font-family: "Montserrat";
    text-decoration: none;
    color: #e0fbfc;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.start::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.start:hover::before {
    left: 100%;
}

.start a{
    text-decoration: none;
}

.start:hover{
    background: linear-gradient(135deg, var(--accent-blue), var(--light-blue));
    color: var(--primary-blue);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
    transform: translateY(-3px) scale(1.05);
    font-weight: 600;
}

.start:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}
/*--------FRONT RIGHT SIDE STYLING---------*/
.front-right{
    height: 80%;
    width: 90%;
    animation: slideInRight 1s ease-out 0.9s both;
    position: relative;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.front img{
    padding-top: 3vw;
    height: 25vw;
    width: 85%;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    filter: brightness(1.1) contrast(1.1);
}

.front img:hover {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    filter: brightness(1.2) contrast(1.2);
}

/*------- FORM SCREEN STYLING----------*/
.entry-sec{
    height: 42vw;
    width: 90vw;
    margin: 0 auto;
    opacity: 0;
    animation: fadeInUp 1s ease-out 1.2s forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.motto-2{
    padding: 5px;
    margin-left: 2%;
    margin-top: 1%;
    color: var(--primary-blue);
    font-size: var(--motto-2);
    font-family: "Montserrat";
    font-weight: 400;
    text-align: center;
    position: relative;
}

.motto-2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--secondary-blue), var(--accent-blue));
    transform: translateX(-50%);
    border-radius: 2px;
}

.motto-2-1{
    padding: 5px;
    margin-left: 2%;
    margin-top: 2%;
    font-size: 2rem;
    font-family: "Montserrat";
    font-weight: 600;
    color: var(--primary-blue);
    text-align: center;
}

.form-body-2{
    display: flex;
    flex-direction: row;
    font-family: "Montserrat";
    gap: 20px;
}

.form{
    background: linear-gradient(135deg, rgba(144, 224, 239, 0.9), rgba(173, 232, 244, 0.9));
    backdrop-filter: blur(10px);
    height: max-content;
    width: 40vw;
    border-radius: 20px;
    margin: -2% auto;
    padding: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--secondary-blue), var(--accent-blue), var(--secondary-blue));
    animation: shimmer 2s infinite;
}

.form:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.formhead p{
    font: "Montserrat";
}

.form-body-2 img{
    height: 30vw;
    width: 30vw ;
    margin: -1% auto;
}

.form-body-2 ul{
    list-style: none;
    font-family: "Montserrat";
}
.form-sizing{
    margin-left: 8%;
    margin-top: 5%;
    font-size: 1.5vw;
    font-family: "Montserrat";
    color: var(--primary-blue);
}

.name{
    background: rgba(240, 248, 255, 0.9);
    border-radius: 25px;
    padding: 1vw;
    border: 2px solid transparent;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    font-family: "Montserrat";
    font-size: 1rem;
    transition: all 0.3s ease;
    width: 100%;
    backdrop-filter: blur(5px);
}

.name:focus {
    outline: none;
    border-color: var(--accent-blue);
    box-shadow: 0 0 20px rgba(72, 202, 228, 0.3);
    transform: scale(1.02);
    background: rgba(255, 255, 255, 0.95);
}

.name::placeholder {
    color: rgba(3, 4, 94, 0.6);
    font-style: italic;
}

/* Enhanced radio buttons */
input[type="radio"] {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid var(--secondary-blue);
    border-radius: 50%;
    margin-right: 10px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

input[type="radio"]:checked {
    border-color: var(--accent-blue);
    background: var(--accent-blue);
    transform: scale(1.1);
}

input[type="radio"]:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

input[type="radio"]:hover {
    box-shadow: 0 0 10px rgba(72, 202, 228, 0.4);
}

/* Enhanced form labels */
.form-sizing label {
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 5px 10px;
    border-radius: 8px;
    display: inline-block;
    margin-left: 5px;
}

.form-sizing label:hover {
    background: rgba(72, 202, 228, 0.1);
    color: var(--primary-blue);
    transform: translateX(3px);
}

.form-sizing li {
    margin: 10px 0;
    transition: all 0.3s ease;
}

.form-sizing li:hover {
    transform: translateX(5px);
}

.submit-btn{
    background: linear-gradient(135deg, var(--secondary-blue), var(--accent-blue));
    height: 2.5rem;
    width: 8vw;
    border-radius: 25px;
    font-size: 1.3vw;
    text-decoration: none;
    margin-left: 30%;
    margin-bottom: 1%;
    font-family: "Montserrat";
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    border: none;
    color: white;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.submit-btn:hover::before {
    left: 100%;
}

.submit-btn:hover{
    background: linear-gradient(135deg, var(--accent-blue), var(--light-blue));
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
    transform: translateY(-3px) scale(1.05);
}

.submit-btn:active {
    transform: translateY(-1px) scale(1.02);
}

/* Form validation styles */
.name.valid {
    border-color: #28a745;
    box-shadow: 0 0 20px rgba(40, 167, 69, 0.3);
}

.name.invalid {
    border-color: #dc3545;
    box-shadow: 0 0 20px rgba(220, 53, 69, 0.3);
}

.validation-message {
    font-size: 0.9rem;
    margin-top: 5px;
    padding: 5px 10px;
    border-radius: 5px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.validation-message.success {
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.validation-message.error {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

/* Loading state */
.submit-btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin-top: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-blue), var(--light-blue));
    width: 0%;
    transition: width 2s ease;
    border-radius: 2px;
}

/* Notification styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 300px;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 1000;
    border-left: 4px solid var(--secondary-blue);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.error {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #fff, #ffe6e6);
}

.notification.success {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #fff, #e6ffe6);
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    margin-left: 15px;
    transition: color 0.3s ease;
}

.notification-close:hover {
    color: #333;
}

/* Particle effects */
.particle-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, var(--accent-blue), transparent);
    border-radius: 50%;
    animation: floatUp linear infinite;
}

@keyframes floatUp {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Scroll animations */
.animate-in {
    animation: slideInUp 0.8s ease-out forwards;
}

/* Ripple effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: rippleEffect 0.6s linear;
    pointer-events: none;
}

@keyframes rippleEffect {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Button positioning for ripple */
.start, .submit-btn {
    position: relative;
    overflow: hidden;
}

/*--------INFORMATION SECTION STYLING---------*/
.info-1{
    margin-left: 3%;
    font-size: var(--ts);
    width: 90%;
    font-family: "Montserrat";
    line-height: 1.6;
    color: var(--primary-blue);
    opacity: 0;
    animation: fadeInUp 1s ease-out 1.5s forwards;
}

.info-sec{
    width: 90vw;
    margin: auto;
    padding: 20px 0;
}

.chakra{
    height: 30%;
    width: 30%;
    float: right;
    border: 3px solid var(--secondary-blue);
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    transition: all 0.4s ease;
    margin: 0 20px 20px 20px;
}

.chakra:hover {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    border-color: var(--accent-blue);
}

.headings{
    padding: 10px 5px;
    font-size: 2rem;
    font-weight: 600;
    margin-left: 5%;
    font-family: "Montserrat";
    color: var(--primary-blue);
    position: relative;
    display: inline-block;
}

.headings::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--secondary-blue), var(--accent-blue));
    transition: width 0.4s ease;
}

.headings:hover::after {
    width: 100%;
}

.vid{
    margin: 2% 5%;
    width: 90vw;
    height: 40vw;
    border: 3px solid var(--secondary-blue);
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    transition: all 0.4s ease;
}

.vid:hover {
    transform: scale(1.02);
    box-shadow: 0 20px 45px rgba(0, 0, 0, 0.4);
    border-color: var(--accent-blue);
}
/*----------FOOTER STYLING----------*/
.footer-sec{
    width: 99.5%;
    background: linear-gradient(135deg, #023e8a 0%, var(--primary-blue) 100%);
    margin-bottom: -1%;
    font-size: var(--ts);
    font-family: "Montserrat";
    position: relative;
    overflow: hidden;
    opacity: 0;
    animation: slideInUp 1s ease-out 1.8s forwards;
}

.footer-sec::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-blue), var(--light-blue), var(--accent-blue));
}

.footer-1{
    display: flex;
    justify-content: space-evenly;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
    font-family: "Montserrat";
    padding: 20px 0;
    position: relative;
}

.footer-1 a{
    text-decoration: none;
    color: white;
    font-family: "Montserrat";
    transition: all 0.3s ease;
    padding: 10px 15px;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.footer-1 a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.footer-1 a:hover::before {
    left: 100%;
}

.footer-1 a:hover {
    color: var(--light-blue);
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.footer-1 img {
    transition: all 0.3s ease;
}

.footer-1 a:hover img {
    transform: scale(1.2) rotate(10deg);
    filter: brightness(1.3);
}

/* .footer-2{
    margin: auto;
    width: 250px;
} */    

/*--------MEDIA QUERIES--------*/
@media only screen and (min-device-width: 1087px) and (max-device-width: 1250px) {
    :root{
        --motto-2: 2rem;
    }
  }
@media only screen and (min-device-width: 823px) and (max-device-width: 1086px) {
    .start{
        width: 7rem;
    }
    :root{
        --motto-2:2rem;
        --ts1: 2.5vw;
    }
}
@media only screen and (min-device-width: 759px) and (max-device-width: 822px) {
    :root{
        --motto-2: 2.2rem;
        --ts1: 2.5vw;
    }
    .start{
        width: 7rem;
    }
    .submit-btn{
        height: 1.5rem;
    }
    .motto-2-1{
        margin-top: 8%;
    }
}
@media only screen and (min-device-width: 675px) and (max-device-width: 758px) {
    :root{
        --ts1: 2.5vw;
        --motto-2: 2rem;
    }
    .motto-2-1{
        margin-top: 12%;
    }
    .start{
        width: 5rem;
    }
    .submit-btn{
        height: 1.5rem;
    }

}
@media only screen and (min-device-width: 631px) and (max-device-width: 675px) {
    :root{
        --ts1: 2.5vw;
        --motto-2: 2rem;
    }
    .motto-2-1{
        margin-top: 12%;
    }
    .start{
        width: 5rem;
        font-size: 1.5vw;
    }
    .submit-btn{
        height: 1.5rem;
    }

}
@media only screen and (min-device-width: 587px) and (max-device-width: 630px) {
    :root{
        --ts1: 2.5vw;
        --motto-2: 2rem;
    }
    .motto-2-1{
        margin-top: 15%;
    }
    .start{
        width: 5rem;
        font-size: 1.5vw;
    }
    .submit-btn{
        height: 1.5rem;
    }

}
@media only screen and (min-device-width: 503px) and (max-device-width: 586px) {
    :root{
        --ts1: 3vw;
        --motto-2: 2rem;
    }
    .motto-2{
        margin-top: 10%;
    }
    .motto-2-1{
        margin-top: 35%;
    }
    .start{
        width: 5rem;
        font-size: 1.5vw;
    }
    .submit-btn{
        height: 1.5rem;
    }
    .form-sizing{
        font-size: 2vw;
    }

}
@media only screen and (min-device-width: 451px) and (max-device-width: 502px) {
    :root{
        --ts1: 3vw;
        --motto-2: 1.5rem;
    }
    .motto-2{
        margin-top: 30%;
    }
    .motto-2-1{
        margin-top: 35%;
    }
    .start{
        width: 5rem;
        font-size: 1.5vw;
    }
    .submit-btn{
        height: 1.5rem;
    }
    .form-sizing{
        font-size: 2vw;
    }
    .form-body-2{
        display: flex;
        align-content: flex-start;
    }
    .name{
        width: 60%;
    }
    .front-left{
        font-size: var(--ts);
    }
}

@media only screen and (min-device-width: 400px) and (max-device-width: 451px) {
    :root{
        --ts1: 3vw;
        --motto-2: 1.5rem;
        --motto:2rem;
    }

    .motto-2{
        margin-top: 40%;
    }
    .motto-2-1{
        margin-top: 35%;
    }
    .start{
        width: 5rem;
        font-size: 1.5vw;
    }
    .submit-btn{
        height: 1.5rem;
    }
    .form-sizing{
        font-size: 2vw;
    }
    .form-body-2{
        display: flex;
        align-content: flex-start;
    }
    .name{
        width: 60%;
    }
    .front-left{
        font-size: var(--ts);
    }
    .info-1{
        font-size: .7rem;
    }
}

@media only screen and (min-device-width: 355px) and (max-device-width: 399px) {
    :root{
        --ts1: 0.8rem;
        --motto-2: 1rem;
        --motto:1.2rem;
    }

    .motto-2{
        margin-top: 80%;
    }
    .motto-2-1{
        margin-top: 35%;
    }
    .start{
        width: 5rem;
        font-size: 1.5vw;
    }
    .submit-btn{
        height: 1rem;
        width: 2rem;
    }
    .form-sizing{
        font-size: 2vw;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }
    .form-body-2{
        display: flex;
        align-content: flex-start;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }
    .name{
        width: 60%;
        padding: 0px;
        font-size: 9px;
    }
    .front-left{
        font-size: var(--ts);
    }
    .headings{
        font-size: 1rem;
    }
    .info-1{
        font-size: .7rem;
    }
}
